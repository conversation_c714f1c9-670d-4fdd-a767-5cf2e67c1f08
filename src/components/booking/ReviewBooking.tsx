import React, { useState, useEffect } from "react";
import { getHotelAndRoomTypes } from "../../utils/dataService";
import PriceBreakdown from "./PriceBreakdown";
import GuestDetailsForm from "./GuestDetailsForm";
import AddOns from "./AddOns";
import { createHotelCart, createCheckoutSession } from "../../utils/store/cart";
import type { Traveler, CartAddOn } from "../../utils/store/cart";
import PoliciesModal from "../ui/PoliciesModal";
import { useUser } from "../../contexts/UserContext";

interface CancellationPolicy {
  id: string;
  name: string;
  description: string;
  days_before_checkin: number;
  refund_type: string;
  refund_amount: number;
}

interface ReviewBookingProps {
  // No props needed - data will come from localStorage
}

// Meal plan mapping
const mealPlanLabels: Record<string, string> = {
  none: "Bed Only",
  bb: "Bed & Breakfast",
  hb: "Half Board",
  fb: "Full Board",
};

const ReviewBooking: React.FC<ReviewBookingProps> = () => {
  // Get user context for auto-populating guest details
  const { user, isAuthenticated } = useUser();

  // State for booking data from localStorage
  const [bookingData, setBookingData] = useState<any>(null);

  // State for hotel and room data
  const [hotel, setHotel] = useState<any>({});
  const [selectedRoom, setSelectedRoom] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // State for price calculations
  const [basePrice, setBasePrice] = useState(0);
  const [discount, setDiscount] = useState(0);
  const [taxesAndFees, setTaxesAndFees] = useState(0);
  const [totalPayable, setTotalPayable] = useState(0);
  const [selectedCoupon] = useState("");
  const [tripInsurance] = useState(false);
  const [insuranceAmount] = useState(59);

  // State for guest details
  const [guestDetails, setGuestDetails] = useState({
    title: "Mr",
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    specialRequests: "",
  });

  // State for additional travelers
  const [travelers, setTravelers] = useState<{
    adults: Traveler[];
    children: Traveler[];
    infants: Traveler[];
  }>({
    adults: [],
    children: [],
    infants: [],
  });

  // State for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});
  const [termsAccepted, setTermsAccepted] = useState(false);

  // State for cancellation policy modal
  const [showPoliciesModal, setShowPoliciesModal] = useState(false);

  // State for upgrade functionality
  const [selectedUpgrade, setSelectedUpgrade] = useState<string>("");

  // State for add-ons
  const [selectedAddOns, setSelectedAddOns] = useState<any[]>([]);
  const [addOnsTotal, setAddOnsTotal] = useState(0);

  // Function to clear specific field error
  const clearFieldError = (fieldName: string) => {
    if (formErrors[fieldName]) {
      setFormErrors((prev) => {
        const newErrors = { ...prev };
        delete newErrors[fieldName];
        return newErrors;
      });
    }
  };

  // Function to handle upgrade update
  const handleUpgradeUpdate = () => {
    if (!selectedUpgrade || !bookingData) return;

    // Update the booking data with the new meal plan
    const updatedBookingData = {
      ...bookingData,
      mealPlan: selectedUpgrade,
    };

    // Update localStorage
    localStorage.setItem("bookingData", JSON.stringify(updatedBookingData));

    // Update local state
    setBookingData(updatedBookingData);

    // Clear the selected upgrade
    setSelectedUpgrade("");
  };

  // Function to handle add-ons changes
  const handleAddOnsChange = (addOns: any[]) => {
    setSelectedAddOns(addOns);
    const total = addOns.reduce((sum, addon) => sum + addon.total_price, 0);
    setAddOnsTotal(total);
  };

  // Load booking data from localStorage
  useEffect(() => {
    try {
      const storedBookingData = localStorage.getItem("bookingData");
      if (storedBookingData) {
        const parsedData = JSON.parse(storedBookingData);
        setBookingData(parsedData);

        // Set initial price values from stored data
        // Use meal plan pricing if available for more accurate calculations
        const currentMealPlan = parsedData.mealPlan || "none";
        const mealPlanPrice =
          parsedData.room?.mealPlanPrices?.[currentMealPlan];

        const totalBaseAmount =
          mealPlanPrice?.total_amount_without_tax ||
          parsedData.basePrice * (parsedData.nights || 1) ||
          parsedData.totalAmount ||
          0;
        const taxAmount =
          mealPlanPrice?.tax_amount || parsedData.taxesAndFees || 0;

        setBasePrice(totalBaseAmount);
        setTaxesAndFees(taxAmount);
        setTotalPayable(totalBaseAmount + taxAmount);

        // If we have hotel and room data in localStorage, use it directly
        if (parsedData.hotel && parsedData.room) {
          setHotel(parsedData.hotel);
          setSelectedRoom(parsedData.room);
          setLoading(false);
        }
      } else {
        setError("No booking data found. Please select a room first.");
        setLoading(false);
      }
    } catch (err) {
      console.error("Error loading booking data from localStorage:", err);
      setError("Failed to load booking data. Please try again.");
      setLoading(false);
    }
  }, []);

  // Fetch hotel and room data (fallback if not available in localStorage)
  useEffect(() => {
    // Only proceed if we have booking data but no hotel/room data
    if (!bookingData) {
      return;
    }

    // If we already have hotel and room data, no need to fetch
    if (hotel && hotel.name && selectedRoom && selectedRoom.name) {
      setLoading(false);
      return;
    }

    // Create a flag to track if the component is mounted
    let isMounted = true;

    const fetchData = async () => {
      // Don't proceed if we've already tried to fetch this data
      if (!bookingData.hotelId || !bookingData.roomId) {
        setError("Missing hotel or room information");
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        const result = await getHotelAndRoomTypes(bookingData.hotelId);

        // Check if component is still mounted before updating state
        if (!isMounted) return;

        if (result) {
          setHotel(result.hotel || {});

          // Find the selected room
          const roomTypes = result.roomTypes || [];
          const room = roomTypes.find(
            (r: any) => r.id.toString() === bookingData.roomId
          );

          if (room) {
            setSelectedRoom(room);
          } else {
            setError("Selected room not found");
          }
        } else {
          setError("Hotel data not found");
        }
      } catch (err) {
        // Check if component is still mounted before updating state
        if (!isMounted) return;

        console.error("Error fetching hotel data:", err);
        setError("Failed to load hotel data");
      } finally {
        // Check if component is still mounted before updating state
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    // Only fetch data if we have both hotelId and roomId but no hotel/room data
    if (
      bookingData.hotelId &&
      bookingData.roomId &&
      (!hotel.name || !selectedRoom.name)
    ) {
      fetchData();
    } else if (!bookingData.hotelId || !bookingData.roomId) {
      setError("Missing hotel or room information");
      setLoading(false);
    } else {
      // We have all the data we need
      setLoading(false);
    }

    // Cleanup function to set the mounted flag to false when the component unmounts
    return () => {
      isMounted = false;
    };
  }, [bookingData?.hotelId, bookingData?.roomId]);

  // Calculate price breakdown
  useEffect(() => {
    if (!bookingData) return;

    // Get pricing information from meal plan data if available, otherwise use stored values
    const currentMealPlan = bookingData.mealPlan || "none";
    const mealPlanPrice = bookingData.room?.mealPlanPrices?.[currentMealPlan];

    // Use meal plan pricing if available, otherwise fallback to stored values
    const perNightAmount =
      mealPlanPrice?.per_night_amount ||
      bookingData.basePrice ||
      bookingData.totalAmount ||
      0;
    const totalBaseAmount =
      mealPlanPrice?.total_amount_without_tax ||
      perNightAmount * (bookingData.nights || 1);
    const storedTaxAmount =
      mealPlanPrice?.tax_amount || bookingData.taxesAndFees || 0;

    // Calculate discount based on selected coupon
    let discountAmount = 0;
    if (selectedCoupon === "MMTDEAL") {
      discountAmount = Math.round(totalBaseAmount * 0.1); // 10% discount
    }

    // Calculate insurance amount if selected
    const insuranceTotal = tripInsurance ? insuranceAmount : 0;

    // Set state values using the calculated pricing
    setBasePrice(totalBaseAmount);
    setTaxesAndFees(storedTaxAmount);
    setDiscount(discountAmount);
    setTotalPayable(
      totalBaseAmount -
        discountAmount +
        storedTaxAmount +
        insuranceTotal +
        addOnsTotal
    );
  }, [
    bookingData,
    selectedCoupon,
    tripInsurance,
    insuranceAmount,
    addOnsTotal,
  ]);

  // Function to scroll to the first error field
  const scrollToFirstError = (errors: Record<string, string>) => {
    const fieldOrder = ["firstName", "lastName", "email", "phone", "terms"];

    for (const field of fieldOrder) {
      if (errors[field]) {
        const element = document.getElementById(field);
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "center",
          });
          element.focus();
          break;
        }
      }
    }
  };

  // Handle proceed to payment - Create cart and redirect to Stripe Checkout
  const handleProceedToPayment = async () => {
    if (!bookingData) {
      setError("Booking data not available");
      return;
    }

    // Validate form
    const errors: Record<string, string> = {};

    if (!guestDetails.firstName.trim()) {
      errors.firstName = "First name is required";
    }

    if (!guestDetails.lastName.trim()) {
      errors.lastName = "Last name is required";
    }

    if (!guestDetails.email.trim()) {
      errors.email = "Email is required";
    } else if (!/\S+@\S+\.\S+/.test(guestDetails.email)) {
      errors.email = "Email is invalid";
    }

    if (!guestDetails.phone.trim()) {
      errors.phone = "Phone number is required";
    }

    if (!termsAccepted) {
      errors.terms = "Please accept the terms and conditions";
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      // Scroll to the first error field
      scrollToFirstError(errors);
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare add-ons for cart creation
      const cartAddOns: CartAddOn[] = selectedAddOns.map((addon) => ({
        service_id: addon.service_id,
        adult_quantity: addon.adult_quantity,
        child_quantity: addon.child_quantity,
      }));

      // Create cart in Medusa backend
      const cartResponse = await createHotelCart({
        hotel_id: bookingData.hotelId,
        room_config_id: bookingData.roomId,
        check_in_date: bookingData.checkIn,
        check_out_date: bookingData.checkOut,
        check_in_time: bookingData.checkInTime,
        check_out_time: bookingData.checkOutTime,
        guest_name: `${guestDetails.firstName} ${guestDetails.lastName}`,
        guest_email: guestDetails.email,
        guest_phone: guestDetails.phone,
        adults: bookingData.guestCount,
        children: bookingData.childrenCount || 0,
        infants: bookingData.infantCount,
        travelers:
          travelers.adults.length > 0 ||
          travelers.children.length > 0 ||
          travelers.infants.length > 0
            ? travelers
            : undefined,
        number_of_rooms: bookingData.roomQuantity,
        total_amount: totalPayable,
        currency_code: bookingData.currencyCode.toLowerCase(),
        region_id: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
        special_requests: guestDetails.specialRequests,
        add_ons: cartAddOns.length > 0 ? cartAddOns : undefined,
        metadata: {
          meal_plan: bookingData.mealPlan || "none",
          room_name: selectedRoom.name || "",
          hotel_name: hotel.name || "",
        },
        shipping_address: {
          first_name: guestDetails.firstName,
          last_name: guestDetails.lastName,
          address_1: "",
          city: "",
          country_code: "US",
          postal_code: "",
          phone: guestDetails.phone,
        },
      });

      if (cartResponse && cartResponse.cart?.id) {
        // Calculate the correct total: basePrice * nights + taxesAndFees
        const correctTotal =
          (bookingData.basePrice || 0) * (bookingData.nights || 1) +
          (bookingData.taxesAndFees || 0);

        // Store cart ID and guest details in booking data for later completion
        const updatedBookingData = {
          ...bookingData,
          cartId: cartResponse.cart.id,
          guestDetails: guestDetails,
          travelers: travelers,
          totalAmount: correctTotal, // Use the correct calculation: basePrice * nights + taxes
          basePrice: bookingData.basePrice,
          taxesAndFees: bookingData.taxesAndFees,
        };
        localStorage.setItem("bookingData", JSON.stringify(updatedBookingData));

        // Create Stripe Checkout session
        const checkoutResponse = await createCheckoutSession(
          cartResponse.cart.id
        );

        if (checkoutResponse?.url) {
          // Redirect to Stripe Checkout
          window.location.href = checkoutResponse.url;
        } else {
          throw new Error("Failed to create checkout session");
        }
      } else {
        throw new Error("Failed to create cart");
      }
    } catch (err) {
      console.error("Error creating cart:", err);
      setError("Failed to process your booking. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date for display - more human-readable format
  const formatDisplayDate = (dateStr: string) => {
    try {
      const date = new Date(dateStr);
      return date.toLocaleDateString("en-US", {
        weekday: "long",
        day: "numeric",
        month: "long",
        year: "numeric",
      });
    } catch (e) {
      return dateStr;
    }
  };

  // Format time for display - convert to 12-hour format
  const formatDisplayTime = (timeStr: string) => {
    try {
      // Parse time string (e.g., "14:00" or "19:00")
      const [hours, minutes] = timeStr.split(":").map(Number);
      const date = new Date();
      date.setHours(hours, minutes, 0, 0);

      return date.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      });
    } catch (e) {
      return timeStr;
    }
  };

  // Get the best cancellation policy (highest refund percentage)
  const getBestCancellationPolicy = (): CancellationPolicy | null => {
    if (
      !hotel?.cancellation_policies ||
      hotel.cancellation_policies.length === 0
    ) {
      return null;
    }

    // Find the policy with the highest refund percentage
    const bestPolicy = hotel.cancellation_policies.reduce(
      (best: CancellationPolicy, current: CancellationPolicy) => {
        // Prioritize policies with refund_type "full" or "percentage"
        if (current.refund_type === "full") {
          return current;
        }

        if (current.refund_type === "percentage") {
          if (best.refund_type === "full") {
            return best; // Keep full refund as best
          }
          if (best.refund_type === "percentage") {
            return current.refund_amount > best.refund_amount ? current : best;
          }
          return current; // Better than no_refund
        }

        // If current is no_refund, only return it if best is also no_refund
        if (
          current.refund_type === "no_refund" &&
          best.refund_type === "no_refund"
        ) {
          return current.days_before_checkin > best.days_before_checkin
            ? current
            : best;
        }

        return best;
      }
    );

    return bestPolicy;
  };

  // Format cancellation policy text
  const formatCancellationPolicyText = (policy: CancellationPolicy | null) => {
    if (!policy) return null;

    const daysText = policy.days_before_checkin === 1 ? "day" : "days";

    if (policy.refund_type === "full") {
      return {
        main: `Free cancellation until ${policy.days_before_checkin} ${daysText} before check-in`,
        sub: "Cancel your booking without any charges",
      };
    } else if (policy.refund_type === "percentage") {
      return {
        main: `${policy.refund_amount}% refund until ${policy.days_before_checkin} ${daysText} before check-in`,
        sub: `Get ${policy.refund_amount}% of your payment back`,
      };
    } else {
      return {
        main: `No refund after ${policy.days_before_checkin} ${daysText} before check-in`,
        sub: "Please check cancellation policy details",
      };
    }
  };

  // Get room image URL
  const getRoomImageUrl = () => {
    // First try to get from stored room data
    if (selectedRoom?.thumbnail) {
      return selectedRoom.thumbnail;
    }
    if (selectedRoom?.images && selectedRoom.images.length > 0) {
      return selectedRoom.images[0];
    }
    // Fallback to original logic
    return (
      selectedRoom?.imageUrl ||
      hotel?.imageUrl ||
      hotel?.images?.[0] ||
      "/images/room-placeholder.jpg"
    );
  };

  return (
    <div className="max-w-7xl mx-auto">
      {/* Back button */}
      <div className="mb-6">
        <button
          id="back-button"
          className="flex items-center p-2 text-sm text-[#3566ab] hover:bg-[#3566ab]/5 border border-[#3566ab]/20 rounded-lg transition-colors duration-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          <span className="ml-1">Back</span>
        </button>
      </div>

      {loading ? (
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#3566ab]"></div>
        </div>
      ) : error ? (
        <div className="bg-red-50 border border-red-200 text-red-700 p-4 rounded-lg">
          {error}
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Booking Details */}
          <div className="lg:col-span-2 space-y-6">
            {/* Hotel Details */}
            <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="md:w-1/4">
                  <img
                    src={hotel?.imageUrl || getRoomImageUrl()}
                    alt={hotel?.name || "Hotel"}
                    className="w-full h-auto rounded-lg object-cover"
                  />
                </div>
                <div className="md:w-3/4">
                  <h2 className="text-xl font-bold mb-1">{hotel.name}</h2>
                  <div className="flex items-center mb-2">
                    {Array.from({ length: hotel.rating || 4 }).map((_, i) => (
                      <svg
                        key={i}
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="currentColor"
                        className="text-yellow-400"
                      >
                        <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                      </svg>
                    ))}
                    <span className="ml-2 text-sm text-gray-600">
                      {hotel.tags?.includes("Couple Friendly")
                        ? "Couple Friendly"
                        : ""}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    {hotel.location || hotel.address}
                  </p>

                  {/* Check-in/Check-out */}
                  <div className="grid grid-cols-2 gap-4 border-t border-gray-200 pt-4">
                    <div>
                      <p className="text-sm text-gray-500 uppercase">
                        CHECK IN
                      </p>
                      <p className="font-bold text-sm">
                        {formatDisplayDate(bookingData?.checkIn || "")} at{" "}
                        {formatDisplayTime(bookingData?.checkInTime || "14:00")}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 uppercase">
                        CHECK OUT
                      </p>
                      <p className="font-bold text-sm">
                        {formatDisplayDate(bookingData?.checkOut || "")} at{" "}
                        {formatDisplayTime(
                          bookingData?.checkOutTime || "11:00"
                        )}
                      </p>
                    </div>
                  </div>

                  <div className="mt-4 text-sm">
                    <span className="font-medium">
                      {bookingData?.nights}
                      {bookingData?.nights > 1 ? " Nights" : " Night"}
                    </span>{" "}
                    |{" "}
                    <span className="font-medium">
                      {bookingData?.guestCount || 1}{" "}
                      {(bookingData?.guestCount || 1) > 1 ? "Adults" : "Adult"}
                      {bookingData?.childrenCount > 0 && (
                        <>
                          , {bookingData.childrenCount}{" "}
                          {bookingData.childrenCount === 1
                            ? "Child"
                            : "Children"}
                        </>
                      )}
                      {bookingData?.infantCount > 0 && (
                        <>
                          , {bookingData.infantCount}{" "}
                          {bookingData.infantCount === 1 ? "Infant" : "Infants"}
                        </>
                      )}
                    </span>{" "}
                  </div>
                </div>
              </div>
            </div>

            {/* Room Details */}
            <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
              <div className="flex items-center mb-4">
                <h3 className="text-lg font-bold text-[#3566ab]">
                  {selectedRoom.name || "Deluxe Room"}
                </h3>
              </div>

              <div className="space-y-2 mb-4">
                <p className="flex items-center text-sm">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 text-green-600"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  Room with{" "}
                  {mealPlanLabels[bookingData?.mealPlan || "none"] ||
                    "Bed Only"}
                </p>
                {selectedRoom?.bedType && (
                  <p className="flex items-center text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2 text-green-600"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    {selectedRoom.bedType.charAt(0).toUpperCase() +
                      selectedRoom.bedType.slice(1)}{" "}
                    Bed
                  </p>
                )}
                {selectedRoom?.size && (
                  <p className="flex items-center text-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="16"
                      height="16"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2 text-green-600"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    {selectedRoom.size} sq.ft
                  </p>
                )}
              </div>
            </div>

            {/* Upgrade Options */}
            <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
              <h3 className="text-lg text-[#3566ab] font-bold mb-4">
                Upgrade Your Stay
              </h3>

              {selectedRoom?.availableMealPlans &&
              selectedRoom.availableMealPlans.length > 1 ? (
                <div className="space-y-4">
                  <div className="space-y-3">
                    {selectedRoom.availableMealPlans.map((mealPlan: string) => {
                      const mealPlanPrice =
                        selectedRoom.mealPlanPrices?.[mealPlan];
                      const currentPrice =
                        selectedRoom.mealPlanPrices?.[
                          bookingData?.mealPlan || "none"
                        ];
                      const priceDifference =
                        mealPlanPrice?.total_amount -
                        (currentPrice?.total_amount || 0);
                      const isCurrentPlan = mealPlan === bookingData?.mealPlan;

                      return (
                        <div key={mealPlan} className="flex items-center">
                          <input
                            type="radio"
                            id={`upgrade-${mealPlan}`}
                            name="upgrade"
                            value={mealPlan}
                            checked={
                              selectedUpgrade === mealPlan ||
                              (selectedUpgrade === "" && isCurrentPlan)
                            }
                            onChange={(e) => setSelectedUpgrade(e.target.value)}
                            className="w-4 h-4 text-[#3566ab] border-gray-300 focus:ring-[#3566ab]"
                          />
                          <label
                            htmlFor={`upgrade-${mealPlan}`}
                            className="ml-2 text-sm font-medium text-gray-900 flex-1 cursor-pointer"
                          >
                            <div className="flex justify-between items-center">
                              <span className="flex items-center">
                                {mealPlanLabels[mealPlan] || mealPlan}
                                {isCurrentPlan && (
                                  <span className="ml-2 text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                                    Current
                                  </span>
                                )}
                              </span>
                              {priceDifference !== 0 && (
                                <span
                                  className={`font-semibold ${
                                    priceDifference > 0
                                      ? "text-[#3566ab]"
                                      : "text-[#3566ab]"
                                  }`}
                                >
                                  {priceDifference > 0 ? "+" : "- "}
                                  {bookingData?.currencyCode || "USD"}{" "}
                                  {Math.abs(priceDifference).toFixed(0)}
                                </span>
                              )}
                              {priceDifference === 0 && !isCurrentPlan && (
                                <span className="text-gray-500 font-medium">
                                  Same price
                                </span>
                              )}
                            </div>
                            <div className="text-xs text-gray-500 mt-1">
                              {mealPlan === "none" &&
                                "Room only, no meals included"}
                              {mealPlan === "bb" && "Includes breakfast"}
                              {mealPlan === "hb" &&
                                "Includes breakfast and dinner"}
                              {mealPlan === "fb" &&
                                "Includes all meals (breakfast, lunch, dinner)"}
                            </div>
                          </label>
                        </div>
                      );
                    })}
                  </div>

                  {/* Update Button */}
                  {selectedUpgrade &&
                    selectedUpgrade !== bookingData?.mealPlan && (
                      <div className="pt-3 border-t border-gray-200">
                        <button
                          onClick={handleUpgradeUpdate}
                          className="w-full py-2 px-4 bg-[#3566ab] text-white rounded-lg font-medium transition-all duration-300 hover:bg-[#3566ab]/90 text-sm"
                        >
                          Update Stay
                        </button>
                      </div>
                    )}
                </div>
              ) : (
                <div className="text-sm text-gray-500">
                  No meal plan upgrades available for this room.
                </div>
              )}
            </div>

            {/* Hotel Rules & Important Information */}
            <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-bold mb-4 text-[#3566ab]">
                Rules & Important Information
              </h3>

              {/* Cancellation Policy Highlight */}
              {(() => {
                const bestPolicy = getBestCancellationPolicy();
                const policyText = formatCancellationPolicyText(bestPolicy);

                if (!policyText) return null;

                const isPositivePolicy =
                  bestPolicy?.refund_type === "full" ||
                  (bestPolicy?.refund_type === "percentage" &&
                    bestPolicy?.refund_amount > 50);

                return (
                  <div
                    className={`mb-4 p-3 rounded-lg border ${
                      isPositivePolicy
                        ? "bg-green-50 border-green-200"
                        : "bg-amber-50 border-amber-200"
                    }`}
                  >
                    <div className="flex items-center">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className={`mr-2 flex-shrink-0 ${
                          isPositivePolicy ? "text-green-600" : "text-amber-600"
                        }`}
                      >
                        {isPositivePolicy ? (
                          <>
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22 4 12 14.01 9 11.01"></polyline>
                          </>
                        ) : (
                          <>
                            <circle cx="12" cy="12" r="10"></circle>
                            <path d="M12 8v4"></path>
                            <path d="M12 16h.01"></path>
                          </>
                        )}
                      </svg>
                      <div>
                        <p
                          className={`text-sm font-medium ${
                            isPositivePolicy
                              ? "text-green-800"
                              : "text-amber-800"
                          }`}
                        >
                          {policyText.main}
                        </p>
                        <p
                          className={`text-xs mt-1 ${
                            isPositivePolicy
                              ? "text-green-600"
                              : "text-amber-600"
                          }`}
                        >
                          {policyText.sub}
                        </p>
                      </div>
                    </div>
                  </div>
                );
              })()}

              <div className="space-y-4">
                {/* Hotel Rules */}
                {hotel?.rules && hotel.rules.length > 0 && (
                  <div>
                    <ul className="space-y-2 text-sm">
                      {hotel.rules.map((rule: string, index: number) => (
                        <li key={index} className="flex items-start">
                          <span className="text-[#3566ab] mr-2">•</span>
                          {rule}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                <div className="bg-gray-50 p-3 rounded-md text-sm">
                  <button
                    onClick={() => setShowPoliciesModal(true)}
                    className="text-[#3566ab] mt-1"
                  >
                    Cancellation policy details
                  </button>
                </div>
              </div>
            </div>

            {/* Add-ons & Services */}
            {bookingData?.hotelId && (
              <AddOns
                hotelId={bookingData.hotelId}
                adults={bookingData?.guestCount || 1}
                children={bookingData?.childrenCount || 0}
                currencyCode={bookingData?.currencyCode || "USD"}
                onAddOnsChange={handleAddOnsChange}
              />
            )}

            {/* Guest Details Form */}
            <GuestDetailsForm
              guestDetails={guestDetails}
              setGuestDetails={setGuestDetails}
              travelers={travelers}
              setTravelers={setTravelers}
              maxAdults={bookingData?.guestCount || 1}
              maxChildren={bookingData?.childrenCount || 0}
              maxInfants={bookingData?.infantCount || 0}
              formErrors={formErrors}
              userData={user?.customer}
              isAuthenticated={isAuthenticated}
              clearFieldError={clearFieldError}
              termsAccepted={termsAccepted}
              setTermsAccepted={setTermsAccepted}
              setShowPoliciesModal={setShowPoliciesModal}
            />
          </div>

          {/* Right Column - Price Breakdown */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              {/* Price Breakdown */}
              <PriceBreakdown
                basePrice={basePrice}
                discount={discount}
                taxesAndFees={taxesAndFees}
                totalPayable={totalPayable}
                currencyCode={bookingData?.currencyCode || "USD"}
                nights={bookingData?.nights || 1}
                addOnsTotal={addOnsTotal}
              />

              {/* Pay Now Button (Desktop) */}
              <div className="hidden lg:block">
                <button
                  onClick={handleProceedToPayment}
                  disabled={isSubmitting}
                  className="w-full py-3 px-6 bg-[#3566ab] text-white rounded-lg font-karla uppercase tracking-wider transition-all duration-300 hover:bg-[#3566ab]/90 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    "Pay Now"
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* Pay Now Button (Mobile & Tablet Only) - Positioned after price breakdown */}
          <div className="lg:hidden col-span-full">
            <button
              onClick={handleProceedToPayment}
              disabled={isSubmitting}
              className="w-full py-3 px-6 bg-[#3566ab] text-white rounded-lg font-karla uppercase tracking-wider transition-all duration-300 hover:bg-[#3566ab]/90 disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                  Processing...
                </>
              ) : (
                "Pay Now"
              )}
            </button>
          </div>
        </div>
      )}

      {/* Cancellation Policies Modal */}
      <PoliciesModal
        isOpen={showPoliciesModal}
        onClose={() => setShowPoliciesModal(false)}
        policies={hotel?.cancellation_policies || []}
      />
    </div>
  );
};

export default ReviewBooking;
