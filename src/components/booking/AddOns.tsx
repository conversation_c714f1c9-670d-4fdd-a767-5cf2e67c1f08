import React, { useState, useEffect } from "react";
import { fetchHotelAddOns, type HotelAddOn } from "../../utils/store/hotels";

interface AddOnSelection {
  service_id: string;
  adult_quantity: number;
  child_quantity: number;
  total_price: number;
}

interface AddOnsProps {
  hotelId: string;
  adults: number;
  children: number;
  currencyCode: string;
  onAddOnsChange: (addOns: AddOnSelection[]) => void;
}

const AddOns: React.FC<AddOnsProps> = ({
  hotelId,
  adults,
  children,
  currencyCode,
  onAddOnsChange,
}) => {
  const [addOns, setAddOns] = useState<HotelAddOn[]>([]);
  const [selectedAddOns, setSelectedAddOns] = useState<AddOnSelection[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch add-ons when component mounts
  useEffect(() => {
    const loadAddOns = async () => {
      try {
        setLoading(true);
        const { add_ons } = await fetchHotelAddOns(hotelId);
        // Filter add-ons that are available for this hotel
        setAddOns(add_ons.filter((addon) => addon.hotel_id.includes(hotelId)));
      } catch (err) {
        console.error("Error loading add-ons:", err);
        setError("Failed to load add-ons");
      } finally {
        setLoading(false);
      }
    };

    if (hotelId) {
      loadAddOns();
    }
  }, [hotelId]);

  // Notify parent component when selections change
  useEffect(() => {
    onAddOnsChange(selectedAddOns);
  }, [selectedAddOns, onAddOnsChange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const handleQuantityChange = (
    serviceId: string,
    type: "adult" | "child",
    quantity: number
  ) => {
    const addOn = addOns.find((a) => a.id === serviceId);
    if (!addOn) return;

    setSelectedAddOns((prev) => {
      const existing = prev.find((s) => s.service_id === serviceId);

      if (existing) {
        // Update existing selection
        const updated = {
          ...existing,
          [type === "adult" ? "adult_quantity" : "child_quantity"]: quantity,
        };

        // Calculate total price
        updated.total_price =
          updated.adult_quantity * addOn.adult_price +
          updated.child_quantity * addOn.child_price;

        // Remove if both quantities are 0
        if (updated.adult_quantity === 0 && updated.child_quantity === 0) {
          return prev.filter((s) => s.service_id !== serviceId);
        }

        return prev.map((s) => (s.service_id === serviceId ? updated : s));
      } else {
        // Create new selection
        if (quantity === 0) return prev; // Don't add if quantity is 0

        const newSelection: AddOnSelection = {
          service_id: serviceId,
          adult_quantity: type === "adult" ? quantity : 0,
          child_quantity: type === "child" ? quantity : 0,
          total_price:
            type === "adult"
              ? quantity * addOn.adult_price
              : quantity * addOn.child_price,
        };

        return [...prev, newSelection];
      }
    });
  };

  const getSelectedQuantity = (
    serviceId: string,
    type: "adult" | "child"
  ): number => {
    const selection = selectedAddOns.find((s) => s.service_id === serviceId);
    return selection
      ? selection[type === "adult" ? "adult_quantity" : "child_quantity"]
      : 0;
  };

  const getTotalAddOnsPrice = (): number => {
    return selectedAddOns.reduce(
      (total, selection) => total + selection.total_price,
      0
    );
  };

  if (loading) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Add-ons & Services
        </h3>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          Add-ons & Services
        </h3>
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  if (addOns.length === 0) {
    return null; // Don't show the section if no add-ons are available
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 p-6">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">
        Add-ons & Services
      </h3>
      <p className="text-sm text-gray-600 mb-6">
        Enhance your stay with our additional services and amenities.
      </p>

      <div className="space-y-4">
        {addOns.map((addOn) => (
          <div key={addOn.id} className="border border-gray-200 rounded-lg p-4">
            <div className="flex justify-between items-start mb-3">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900">{addOn.name}</h4>
                {addOn.description && (
                  <p className="text-sm text-gray-600 mt-1">
                    {addOn.description}
                  </p>
                )}
                <div className="mt-2 text-sm text-gray-700">
                  <span className="font-medium">
                    Adults: {formatCurrency(addOn.adult_price)}
                  </span>
                  {children > 0 && (
                    <span className="ml-4 font-medium">
                      Children: {formatCurrency(addOn.child_price)}
                    </span>
                  )}
                </div>
              </div>
              {addOn.images && addOn.images.length > 0 && (
                <img
                  src={addOn.images[0]}
                  alt={addOn.name}
                  className="w-16 h-16 object-cover rounded-lg ml-4"
                />
              )}
            </div>

            <div className="flex items-center space-x-6">
              {/* Adult quantity selector */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Adults:</span>
                <div className="flex items-center border border-gray-300 rounded">
                  <button
                    type="button"
                    onClick={() =>
                      handleQuantityChange(
                        addOn.id,
                        "adult",
                        Math.max(0, getSelectedQuantity(addOn.id, "adult") - 1)
                      )
                    }
                    className="px-2 py-1 text-gray-600 hover:bg-gray-100"
                    disabled={getSelectedQuantity(addOn.id, "adult") === 0}
                  >
                    -
                  </button>
                  <span className="px-3 py-1 text-sm font-medium">
                    {getSelectedQuantity(addOn.id, "adult")}
                  </span>
                  <button
                    type="button"
                    onClick={() =>
                      handleQuantityChange(
                        addOn.id,
                        "adult",
                        Math.min(
                          adults,
                          getSelectedQuantity(addOn.id, "adult") + 1
                        )
                      )
                    }
                    className="px-2 py-1 text-gray-600 hover:bg-gray-100"
                    disabled={getSelectedQuantity(addOn.id, "adult") >= adults}
                  >
                    +
                  </button>
                </div>
              </div>

              {/* Child quantity selector */}
              {children > 0 && (
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">Children:</span>
                  <div className="flex items-center border border-gray-300 rounded">
                    <button
                      type="button"
                      onClick={() =>
                        handleQuantityChange(
                          addOn.id,
                          "child",
                          Math.max(
                            0,
                            getSelectedQuantity(addOn.id, "child") - 1
                          )
                        )
                      }
                      className="px-2 py-1 text-gray-600 hover:bg-gray-100"
                      disabled={getSelectedQuantity(addOn.id, "child") === 0}
                    >
                      -
                    </button>
                    <span className="px-3 py-1 text-sm font-medium">
                      {getSelectedQuantity(addOn.id, "child")}
                    </span>
                    <button
                      type="button"
                      onClick={() =>
                        handleQuantityChange(
                          addOn.id,
                          "child",
                          Math.min(
                            children,
                            getSelectedQuantity(addOn.id, "child") + 1
                          )
                        )
                      }
                      className="px-2 py-1 text-gray-600 hover:bg-gray-100"
                      disabled={
                        getSelectedQuantity(addOn.id, "child") >= children
                      }
                    >
                      +
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Show total price for this add-on if selected */}
            {selectedAddOns.find((s) => s.service_id === addOn.service_id) && (
              <div className="mt-3 pt-3 border-t border-gray-200">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Subtotal:</span>
                  <span className="font-medium text-[#3566ab]">
                    {formatCurrency(
                      selectedAddOns.find(
                        (s) => s.service_id === addOn.service_id
                      )?.total_price || 0
                    )}
                  </span>
                </div>
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Total add-ons price */}
      {selectedAddOns.length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200">
          <div className="flex justify-between items-center">
            <span className="font-medium text-gray-900">Total Add-ons:</span>
            <span className="font-semibold text-lg text-[#3566ab]">
              {formatCurrency(getTotalAddOnsPrice())}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddOns;
